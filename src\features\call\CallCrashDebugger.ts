/**
 * Call Crash Debugger - Monitor and debug call-related crashes
 */

import WebRTCService from './WebRTCService';

interface CrashLog {
  timestamp: number;
  event: string;
  error?: any;
  callState?: any;
  stackTrace?: string;
}

class CallCrashDebugger {
  private static instance: CallCrashDebugger;
  private crashLogs: CrashLog[] = [];
  private isMonitoring = false;

  private constructor() {
    this.setupGlobalErrorHandlers();
  }

  static getInstance(): CallCrashDebugger {
    if (!CallCrashDebugger.instance) {
      CallCrashDebugger.instance = new CallCrashDebugger();
    }
    return CallCrashDebugger.instance;
  }

  // Setup global error handlers
  private setupGlobalErrorHandlers(): void {
    // Handle unhandled promise rejections
    if (typeof global !== 'undefined') {
      const originalHandler = global.onunhandledrejection;
      global.onunhandledrejection = (event: any) => {
        this.logCrash('Unhandled Promise Rejection', event.reason);
        if (originalHandler) {
          originalHandler(event);
        }
      };

      // Handle uncaught exceptions
      const originalErrorHandler = global.onerror;
      global.onerror = (message: any, source?: string, lineno?: number, colno?: number, error?: Error) => {
        this.logCrash('Uncaught Exception', {
          message,
          source,
          lineno,
          colno,
          error: error?.message,
          stack: error?.stack
        });
        if (originalErrorHandler) {
          return originalErrorHandler(message, source, lineno, colno, error);
        }
        return false;
      };
    }
  }

  // Start monitoring call operations
  startMonitoring(): void {
    console.log('🚨 Starting call crash monitoring...');
    this.isMonitoring = true;
    this.logEvent('Monitoring Started');
  }

  // Stop monitoring
  stopMonitoring(): void {
    console.log('🚨 Stopping call crash monitoring');
    this.isMonitoring = false;
    this.logEvent('Monitoring Stopped');
  }

  // Log a crash event
  logCrash(event: string, error?: any): void {
    const callState = WebRTCService.getCallState();
    
    const crashLog: CrashLog = {
      timestamp: Date.now(),
      event,
      error: error ? {
        message: error.message || error,
        name: error.name,
        stack: error.stack
      } : undefined,
      callState: { ...callState },
      stackTrace: new Error().stack
    };

    this.crashLogs.push(crashLog);
    
    // Keep only last 50 logs
    if (this.crashLogs.length > 50) {
      this.crashLogs = this.crashLogs.slice(-50);
    }

    console.error('🚨 CRASH DETECTED:', event, error);
    console.error('🚨 Call State at crash:', callState);
  }

  // Log a regular event
  logEvent(event: string): void {
    if (!this.isMonitoring) return;

    const callState = WebRTCService.getCallState();
    
    const log: CrashLog = {
      timestamp: Date.now(),
      event,
      callState: { ...callState }
    };

    this.crashLogs.push(log);
    
    // Keep only last 50 logs
    if (this.crashLogs.length > 50) {
      this.crashLogs = this.crashLogs.slice(-50);
    }

    console.log('📝 Event logged:', event);
  }

  // Wrap accept call with crash monitoring
  monitorAcceptCall(originalAcceptCall: () => Promise<void>): () => Promise<void> {
    return async () => {
      try {
        this.logEvent('Accept Call Started');
        await originalAcceptCall();
        this.logEvent('Accept Call Completed');
      } catch (error) {
        this.logCrash('Accept Call Failed', error);
        throw error; // Re-throw for UI handling
      }
    };
  }

  // Get crash report
  getCrashReport(): void {
    console.log('🚨 Call Crash Report');
    console.log('=====================================');

    if (this.crashLogs.length === 0) {
      console.log('✅ No crashes or events logged');
      return;
    }

    const crashes = this.crashLogs.filter(log => log.error);
    const events = this.crashLogs.filter(log => !log.error);

    console.log(`📊 Total Events: ${events.length}`);
    console.log(`🚨 Total Crashes: ${crashes.length}`);

    if (crashes.length > 0) {
      console.log('\n🚨 CRASH DETAILS:');
      crashes.forEach((crash, index) => {
        const time = new Date(crash.timestamp).toLocaleTimeString();
        console.log(`\n${index + 1}. ${time} - ${crash.event}`);
        console.log('   Error:', crash.error?.message || 'Unknown error');
        console.log('   Call State:', crash.callState);
        if (crash.error?.stack) {
          console.log('   Stack:', crash.error.stack.substring(0, 200) + '...');
        }
      });
    }

    console.log('\n📝 RECENT EVENTS:');
    const recentEvents = this.crashLogs.slice(-10);
    recentEvents.forEach((log, index) => {
      const time = new Date(log.timestamp).toLocaleTimeString();
      const type = log.error ? '🚨' : '📝';
      console.log(`${type} ${time} - ${log.event}`);
    });

    console.log('=====================================');
  }

  // Analyze crash patterns
  analyzeCrashPatterns(): void {
    console.log('🔍 Analyzing Crash Patterns');
    console.log('=====================================');

    const crashes = this.crashLogs.filter(log => log.error);
    
    if (crashes.length === 0) {
      console.log('✅ No crashes to analyze');
      return;
    }

    // Group by error type
    const errorTypes: { [key: string]: number } = {};
    crashes.forEach(crash => {
      const errorType = crash.error?.name || 'Unknown';
      errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
    });

    console.log('📊 Error Types:');
    Object.entries(errorTypes).forEach(([type, count]) => {
      console.log(`   ${type}: ${count} occurrences`);
    });

    // Check for timing patterns
    const acceptCallCrashes = crashes.filter(crash => 
      crash.event.includes('Accept Call')
    );

    if (acceptCallCrashes.length > 0) {
      console.log('\n🎯 Accept Call Crashes:');
      acceptCallCrashes.forEach(crash => {
        console.log(`   - ${crash.error?.message || 'Unknown error'}`);
        console.log(`     State: isInCall=${crash.callState?.isInCall}, isIncoming=${crash.callState?.isIncoming}`);
      });
    }

    console.log('=====================================');
  }

  // Clear crash logs
  clearLogs(): void {
    this.crashLogs = [];
    console.log('🗑️ Crash logs cleared');
  }

  // Get safe accept call function
  getSafeAcceptCall(): () => Promise<void> {
    return async () => {
      try {
        this.logEvent('Safe Accept Call Started');
        
        // Add extra safety checks
        const callState = WebRTCService.getCallState();
        if (!callState.isInCall) {
          throw new Error('Not in call state');
        }

        await WebRTCService.acceptCall();
        this.logEvent('Safe Accept Call Completed');
      } catch (error) {
        this.logCrash('Safe Accept Call Failed', error);
        
        // Try to recover gracefully
        console.log('🔄 Attempting graceful recovery...');
        try {
          // Reset WebRTC service if needed
          WebRTCService.forceReset();
          this.logEvent('WebRTC Service Reset');
        } catch (resetError) {
          this.logCrash('Recovery Failed', resetError);
        }
        
        throw error;
      }
    };
  }
}

// Make available globally for debugging
if (typeof global !== 'undefined') {
  (global as any).CallCrashDebugger = CallCrashDebugger.getInstance();
}

export default CallCrashDebugger.getInstance();
